import api from "./";

/**
 * Get all devices for a facility.
 * @param {string} facilityId - The ID of the facility.
 * @returns {Promise<any>} A promise that resolves to the list of devices.
 */
export const getDevicesForFacility = async (facilityId) => {
  const response = await api.get(`/facility/devices/${facilityId}`);
  return response.data;
};

/**
 * Create a new device in a facility.
 * @param {string} facilityId - The ID of the facility.
 * @param {object} deviceData - The data for the new device.
 * @returns {Promise<any>} A promise that resolves to the created device data.
 */
export const createDeviceInFacility = async (facilityId, deviceData) => {
  const response = await api.post(`/facility/devices/${facilityId}`, deviceData);
  return response.data;
};

/**
 * Get a specific device by ID in a facility.
 * @param {string} facilityId - The ID of the facility.
 * @param {string} deviceId - The ID of the device.
 * @returns {Promise<any>} A promise that resolves to the device data.
 */
export const getDeviceById = async (facilityId, deviceId) => {
  const response = await api.get(`/facility/devices/${facilityId}/${deviceId}`);
  return response.data;
};

/**
 * Update a device in a facility.
 * @param {string} facilityId - The ID of the facility.
 * @param {string} deviceId - The ID of the device.
 * @param {object} updateData - The data to update the device with.
 * @returns {Promise<any>} A promise that resolves to the updated device data.
 */
export const updateDevice = async (facilityId, deviceId, updateData) => {
  const response = await api.patch(`/facility/devices/${facilityId}/${deviceId}`, updateData);
  return response.data;
};
